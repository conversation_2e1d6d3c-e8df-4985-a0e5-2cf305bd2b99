// import { use } from "react";

export const API_CONFIG = {
  BASE_URL: process.env.REACT_APP_API_URL || 'http://localhost:3014',
  WLP_URL: process.env.WLP_API_URL ||'https://zds.zalopay.vn/be',
  HEADERS: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  TIMEOUT: 30000, // 30 seconds
};

export const ENDPOINTS = {
  // Auth endpoints
  AUTH: {
    LOGIN: '/auth/ldaplogin',
    LOGOUT: '/auth/logout',
    PROFILE: '/auth/profile',
  },
  // User endpoints
  USERS: {
    REGISTER: '/users/register',
    UPDATE: '/users/update',
    // for all platforms
    GET_ALL: (platform) => `/users/get_all?platform=${platform}`,
    GET_ALL_PLATFORM: (username) => `/users/get_all_platform?username=${username}`,
    GET: '/users/get',
    GET_BASIC: (role) => `/users/get_basic?role=${role}`,
    WLP_CREATE: '/users/wlp/create',
    WLP_UPDATE: '/users/wlp/update',
    ADS_CREATE: '/users/ads/create',
    ADS_UPDATE: '/users/ads/update',
    SBV_CREATE: '/users/sbv/create',
    SBV_UPDATE: '/users/sbv/update',
    ECOMMERCE_CREATE: '/users/ecommerce/create',
    ECOMMERCE_UPDATE: '/users/ecommerce/update',
    MYSQL_CREATE: '/users/mysql/create',
    MYSQL_UPDATE: '/users/mysql/update',
  },
  NOTIFICATION: {
    GET: (userId) => `/notifications/get?user_id=${userId}`,
    CREATE: '/notifications/create',
    MARK_AS_READ: (notificationId) => `/notifications/mark-as-read?notification_id=${notificationId}`,
    // DELETE: '/notification/delete',
  },
  REQUEST: {
    CREATE: '/requests/create',
    GET: (id) => `/requests/get?request_id=${id}`,
    GET_BY_USER: (id) => `/requests/getbyuser?user_id=${id}`,
    UPDATE: (requestId) => `/requests/update?request_id=${requestId}`,
    // UPDATE_STATUS: (requestId) => `/requests/update_status?request_id=${requestId}`,
    DELETE: (requestId) => `/requests/delete?request_id=${requestId}`,
  },
  CONFIG: {
    GET: '/config/approval/get',
    CREATE: '/config/approval/create',
    UPDATE: (id) => `/config/approval/update?config_id=${id}`,
    DELETE: (id) => `/config/approval/delete?config_id=${id}`,
  },
  // Statistics endpoints
  STATS: {
    CONDITION_UPDATED: '/api/stats/conditionupdated',
    DATA_UPDATED: '/api/stats/dataupdated',
  },
  // Release Notes endpoints
  RELEASE: {
    GET_ALL: '/release/notes',
    GET: (id) => `/release/note?note_id=${id}`,
    CREATE: '/release/note/create',
    UPDATE: (id) => `/release/note/update?note_id=${id}`,
    DELETE: (id) => `/release/note/delete?note_id=${id}`,
  },
  ATLAS: {
    GET_REPORT_OWNERS: '/atlas/owners',
    GET_WORKBOOKS: '/atlas/workbooks',
    GET_VIEWS: '/atlas/views',
    GET_WORKBOOK_VIEWS: (id) => `/atlas/workbooks/${id}/views`,
    GET_THUMBNAIL: '/atlas/thumbnail',
  },
  // Feedback endpoints
  FEEDBACK: {
    CREATE: '/feedback/create',
    GET_ALL: '/feedback/get_all',
    GET_BY_USER: '/feedback/get_by_user',
    // GET: (id) => `/feedback/get?feedback_id=${id}`,
    // UPDATE: (id) => `/feedback/update?feedback_id=${id}`,
    // DELETE: (id) => `/feedback/delete?feedback_id=${id}`,
  },
  // File upload endpoints
  FILES: {
    UPLOAD: '/files/upload',
    DOWNLOAD: (id) => `/files/download?file_id=${id}`,
    DELETE: (id) => `/files/delete?file_id=${id}`,
    GET_BY_USER: '/files/get_by_user',
    GET_ALL: '/files/get_all',
  },
  // Add more endpoint groups as needed
};