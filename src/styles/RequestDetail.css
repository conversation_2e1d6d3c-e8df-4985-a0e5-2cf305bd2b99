/* Modern Request Detail Container */
.request-detail-container {
  /* max-width: 1200px; */
  margin: 0 auto;
  background-color: transparent;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.request-detail-container.loading,
.request-detail-container.error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  text-align: center;
  background-color: var(--section-bg, white);
  border-radius: 12px;
  box-shadow: var(--card-shadow, 0 2px 8px rgba(0, 0, 0, 0.04));
  padding: 30px;
}

.loading-icon {
  font-size: 36px;
  color: var(--primary-blue, #4c6ef5);
  margin-bottom: 16px;
  animation: spin 1.5s linear infinite;
}

.loading-text {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-secondary, #666);
}

.error-icon {
  font-size: 36px;
  color: #f44336;
  margin-bottom: 16px;
}

.error-message {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-secondary, #666);
  margin-bottom: 20px;
}

.btn-primary {
  background-color: var(--primary-blue, #4c6ef5);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--primary-blue-darker, #3b5bdb);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.btn-primary:disabled {
  background-color: var(--text-muted, #999);
  color: rgba(255, 255, 255, 0.7);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
  opacity: 0.6;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Modern Header with Breadcrumb */
.request-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  position: relative;
}

/* Header Left Section */
.header-left {
  display: flex;
  align-items: center;
  gap: 24px;
}

/* Modern Back Button */
.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: var(--hover-bg, #f5f7fb);
  border: none;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-secondary, #666);
  transition: all 0.2s ease;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: var(--text-secondary, #666);
}

.breadcrumb-separator {
  font-size: 12px;
  color: var(--text-muted, #999);
}

.current-page {
  font-weight: 500;
  color: var(--text-color, #333);
}

.back-button:hover {
  color: var(--text-color, #333);
  background: var(--hover-bg, #e9ecf3);
  transform: translateY(-1px);
}

/* Title Section */
.request-title-section {
  background-color: var(--section-bg, white);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  padding: 24px;
  margin-bottom: 24px;
  display: grid;
  grid-template-columns: 3fr 2fr;
  gap: 24px;
  align-items: center;
}

.request-title-left {
  display: flex;
  flex-direction: column;
}

.request-title-right {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.request-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.btn-action {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border-radius: 8px;
  font-size: 18px;
  font-weight: 500;
  cursor: pointer;
  border: none;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.btn-action:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.btn-reject {
  background-color: #ffebee;
  color: #c62828;
}

.btn-reject:hover {
  background-color: #ffcdd2;
}

.btn-approve {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.btn-approve:hover {
  background-color: #c8e6c9;
}

.btn-review {
  background-color: #e0f7fa;
  color: #0097a7;
}

.btn-review:hover {
  background-color: #b2ebf2;
}

.btn-process {
  background-color: #e3f2fd;
  color: #1565c0;
}

.btn-process:hover {
  background-color: #bbdefb;
}

.btn-complete {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.btn-complete:hover {
  background-color: #c8e6c9;
}

.request-title-section h1 {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 16px 0;
  color: var(--text-color, #333);
  letter-spacing: -0.5px;
}

.request-meta {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-secondary, #666);
  font-size: 14px;
}

.meta-icon {
  color: var(--primary-blue, #4c6ef5);
}

/* Modern Status Flow */
.request-detail-status-flow {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 0 0 24px 0;
  padding: 24px;
  position: relative;
  background-color: var(--section-bg, white);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.status-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  flex: 1;
  transition: transform 0.3s ease;
}

.step-icon {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  background-color: var(--btn-secondary-bg, #f5f5f5);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  color: var(--text-muted, #999);
  border: 2px solid var(--border-color, #eee);
  transition: all 0.3s;
  font-size: 18px;
}

.step-label {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-muted, #999);
  text-align: center;
  transition: color 0.3s;
}

.step-connector {
  position: absolute;
  height: 2px;
  background-color: var(--border-color, #eee);
  top: 22px;
  left: 53%;
  right: -50%;
  z-index: 1;
  transition: background-color 0.3s, transform 0.3s;
}

/* Active Steps - More Vibrant */
.status-step.active .step-icon {
  background-color: rgba(76, 175, 80, 0.15);
  border-color: #4caf50;
  color: #4caf50;
  transform: scale(1.05);
}

.status-step.active .step-label {
  color: var(--text-color, #333);
}

.status-step.current .step-icon {
  background-color: #66af68;
  border-color: #4caf50;
  color: white;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
  transform: scale(1.15);
}

.step-connector.active {
  background-color: #4caf50;
  height: 3px;
}

/* Connector for rejected step should be red */
.status-step.active:last-child .step-connector.active {
  background-color: #c62828;
}

/* Rejected step */
.status-step.active[data-step-key="rejected"] .step-icon {
  background-color: rgba(198, 40, 40, 0.1);
  border-color: #c62828;
  color: #c62828;
}

.status-step.current[data-step-key="rejected"] .step-icon {
  background-color: #ec8383;
  color: white;
  box-shadow: 0 2px 10px rgba(198, 40, 40, 0.3);
}

/* Modern Request Detail Body - 2 Column Layout */
.request-detail-body {
  display: grid;
  grid-template-columns: 3fr 2fr;
  gap: 24px;
}

.request-detail-column {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Section Cards */
.request-detail-section {
  background-color: var(--section-bg, white);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  padding: 24px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
  height: auto; /* Allow sections to grow based on content */
  min-height: 200px; /* Minimum height for visual consistency */
}

.request-detail-section h2 {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 20px 0;
  color: var(--text-color, #333);
  display: flex;
  align-items: center;
  gap: 8px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--border-color, #eee);
}

/* Request Information Section */
.request-detail-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  flex: 1;
}

.info-row {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-label {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-secondary, #666);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-value {
  font-size: 15px;
  color: var(--text-color, #333);
  word-break: break-word;
  line-height: 1.4;
}

.description-row {
  margin-top: 8px;
  padding-top: 16px;
  border-top: 1px dashed var(--border-color, #eee);
}

.info-value.description {
  font-weight: normal;
}

/* Styling for Quill Editor HTML content in description */
.info-value.description p,
.info-value p {
  margin: 0 0 10px 0;
}

.info-value.description strong,
.info-value strong {
  color: rgb(230, 0, 0);
}

/* Additional Quill Editor styles */
.info-value.description ul,
.info-value.description ol,
.info-value ul,
.info-value ol {
  margin-left: 20px;
  margin-bottom: 10px;
}

.info-value.description a,
.info-value a {
  color: var(--primary-blue, #4c6ef5);
  text-decoration: underline;
}

.info-value.description blockquote,
.info-value blockquote {
  border-left: 3px solid #ccc;
  margin-left: 5px;
  padding-left: 10px;
  color: var(--text-secondary, #666);
}

.info-value.description img,
.info-value img {
  max-width: 100%;
  height: auto;
}

/* Modern Timeline */
.request-timeline {
  display: flex;
  flex-direction: column;
  gap: 24px;
  position: relative;
  padding: 8px 0;
  flex: 1;
}

.timeline-item {
  display: flex;
  gap: 18px;
  position: relative;
  /* padding-bottom: 24px; */
  transition: transform 0.2s ease;
}

.timeline-item:hover {
  transform: translateX(4px);
}

.timeline-item:before {
  content: '';
  position: absolute;
  top: 40px;
  bottom: 0;
  left: 20px;
  width: 2px;
  background-color: var(--border-color, #eee);
  z-index: 1;
  border-radius: 1px;
}

.timeline-item:last-child:before {
  display: none;
}

.timeline-icon {
  z-index: 2;
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--hover-bg, #f5f7fb);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-blue, #4c6ef5);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.timeline-item:hover .timeline-icon {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.timeline-content {
  flex: 1;
}

.timeline-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color, #333);
  margin-bottom: 6px;
}

.timeline-time {
  font-size: 12px;
  color: var(--text-secondary, #666);
  margin-bottom: 8px;
}

.timeline-description {
  font-size: 14px;
  color: var(--text-secondary, #555);
}

.timeline-value {
  font-size: 14px;
  color: var(--text-color, #333);
  margin-top: 8px;
  padding: 4px 0;
}

.timeline-status {
  display: inline-flex;
  align-items: center;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: capitalize;
}

.timeline-status.pending {
  background-color: #fff8e1;
  color: #f57c00;
}

.timeline-status.reviewed {
  background-color: #e0f7fa;
  color: #0097a7;
}

.timeline-status.approved {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.timeline-status.in_progress {
  background-color: #e3f2fd;
  color: #1565c0;
}

.timeline-status.done {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.timeline-status.rejected {
  background-color: #ffebee;
  color: #c62828;
}

.timeline-user {
  font-size: 12px;
  color: var(--text-secondary, #666);
  margin-top: 8px;
  font-style: italic;
}

/* Modern People Section */
.request-people {
  display: flex;
  flex-direction: column;
  gap: 16px;
  flex: 1;
}

/* Comment Section Styles */
.comment-section {
  display: flex;
  flex-direction: column;
}

.request-comments {
  display: flex;
  flex-direction: column;
  gap: 20px;
  flex: 1;
  border-radius: 16px;
}

.comments-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding-right: 10px;
  border-radius: 16px;
}

.comment-item {
  display: flex;
  gap: 16px;
  background-color: transparent;
  padding: 16px 0;
  transition: background-color 0.2s ease;
  margin-bottom: 0;
  position: relative;
  border-bottom: none;
  border-radius: 16px;
}

.comment-item:hover {
  background-color: var(--hover-bg, rgba(245, 247, 251, 0.2));
  border-radius: 16px;

}

.comment-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(63, 66, 84, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #3f4254;
  font-size: 18px;
  flex-shrink: 0;
  overflow: hidden;
}

.comment-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.comment-content {
  flex: 1;
  border-radius: 16px;
}

.comment-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
  /* margin-bottom: 4px; */
}

.comment-author {
  font-weight: 600;
  font-size: 14px;
  color: var(--text-color, #333);
}

.comment-time {
  font-size: 12px;
  color: var(--text-secondary, #666);
  font-weight: normal;
}

.comment-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 4px;
  border-radius: 16px;

}
.comment-edited {
  font-style: italic;
  color: var(--text-muted, #999);
  font-size: 11px;
}

.comment-actions {
  display: flex;
  gap: 8px;
}

.comment-action-btn {
  background: none;
  border: none;
  padding: 2px;
  cursor: pointer;
  color: var(--text-secondary, #666);
  font-size: 14px;
  opacity: 0.6;
  transition: opacity 0.2s ease, color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.comment-action-btn:hover {
  opacity: 1;
  color: var(--primary-blue, #4c6ef5);
}

.comment-action-btn.delete:hover {
  color: #f44336;
}

.comment-text, .reply-text {
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-color, #333);
  word-break: break-word;
  white-space: pre-wrap; /* Preserve formatting */
  font-family: inherit; /* Use the same font as the rest of the app */
  margin-bottom: 8px;
}

.comment-footer {
  display: flex;
  justify-content: flex-start;
  margin-top: 8px;
  gap: 16px;
}

.comment-reply-btn {
  background: none;
  border: none;
  padding: 4px 0;
  font-size: 13px;
  font-weight: 500;
  color: var(--text-secondary, #666);
  cursor: pointer;
  transition: color 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.comment-reply-btn:hover {
  color: var(--primary-blue, #4c6ef5);
}

/* Style for code or monospaced text in comments */
.comment-text code,
.comment-text pre,
.reply-text code,
.reply-text pre {
  font-family: 'Courier New', Courier, monospace;
  background-color: rgba(0, 0, 0, 0.05);
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 13px;
}

/* Multi-line code blocks */
.comment-text pre,
.reply-text pre {
  padding: 8px;
  margin: 8px 0;
  overflow-x: auto;
  /* border-left: 3px solid var(--primary-blue, #4c6ef5); */
}

.no-comments {
  text-align: center;
  padding: 30px 0;
  color: var(--text-secondary, #666);
  font-style: italic;
}

.comment-form {
  margin-top: 16px;
}

.comment-input-container {
  display: flex;
  gap: 12px;
  position: relative;
}

.comment-input {
  flex: 1;
  border: 1px solid var(--border-color, #eee);
  border-radius: 8px;
  padding: 12px;
  min-height: 80px;
  height: 80px; /* Initial height */
  resize: none; /* Disable manual resize since we're auto-resizing */
  overflow: hidden; /* Hide scrollbar */
  font-family: inherit;
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-color, #333);
  background-color: var(--content-bg, white);
  transition: border-color 0.2s ease, box-shadow 0.2s ease, height 0.1s ease;
  position: relative;
  max-height: 300px; /* Set a maximum height */
}

.comment-input::placeholder {
  color: var(--text-muted, #999);
  font-style: italic;
  font-size: 13px;
}

.comment-input:focus {
  outline: none;
  border-color: var(--primary-blue, #4c6ef5);
  box-shadow: 0 0 0 2px rgba(76, 110, 245, 0.1);
}

.comment-submit {
  align-self: flex-end;
  background-color: var(--primary-blue, #4c6ef5);
  color: white;
  border: none;
  border-radius: 8px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s ease, transform 0.2s ease;
  position: relative;
}

.comment-submit::after {
  content: '⌘↵';
  position: absolute;
  bottom: -24px;
  right: 0;
  font-size: 11px;
  color: var(--text-muted, #999);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.comment-input-container:hover .comment-submit::after {
  opacity: 1;
}

.comment-submit:hover:not(:disabled) {
  background-color: var(--primary-blue-darker, #3b5bdb);
  transform: translateY(-2px);
}

.comment-submit:disabled {
  background-color: var(--border-color, #eee);
  color: var(--text-muted, #999);
  cursor: not-allowed;
}

/* Comment Edit Form */
.comment-edit-form {
  margin-top: 8px;
  margin-bottom: 16px;
}

.comment-edit-input {
  width: 100%;
  border: 1px solid var(--border-color, #eee);
  border-radius: 8px;
  padding: 12px;
  min-height: 80px;
  resize: vertical;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-color, #333);
  background-color: var(--content-bg, white);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  margin-bottom: 8px;
}

.comment-edit-input:focus {
  outline: none;
  border-color: var(--primary-blue, #4c6ef5);
  box-shadow: 0 0 0 2px rgba(76, 110, 245, 0.1);
}

.comment-edit-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.btn-cancel, .btn-save, .btn-reply {
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-cancel {
  background-color: var(--hover-bg, #f5f7fb);
  color: var(--text-secondary, #666);
  border: 1px solid var(--border-color, #eee);
}

.btn-cancel:hover {
  background-color: var(--hover-bg-darker, #e9ecf3);
}

.btn-save, .btn-reply {
  background-color: var(--primary-blue, #4c6ef5);
  color: white;
  border: none;
}

.btn-save:hover, .btn-reply:hover {
  background-color: var(--primary-blue-darker, #3b5bdb);
  transform: translateY(-1px);
}

/* Comment Reply Form */
.comment-reply-form {
  margin-top: 8px;
  margin-bottom: 16px;
}

.comment-reply-input {
  width: 100%;
  border: 1px solid var(--border-color, #eee);
  border-radius: 8px;
  padding: 12px;
  min-height: 60px;
  resize: vertical;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-color, #333);
  background-color: var(--content-bg, white);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  margin-bottom: 8px;
}

.comment-reply-input:focus {
  outline: none;
  border-color: var(--primary-blue, #4c6ef5);
  box-shadow: 0 0 0 2px rgba(76, 110, 245, 0.1);
}

.comment-reply-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

/* Comment Replies - YouTube style */
.comment-replies {
  margin-top: 8px;
  /* padding-left: 40px; Reduced space to save horizontal space */
  position: relative;
}

/* Connection lines for replies - Starting from avatar */
.comment-replies::before {
  content: '';
  position: absolute;
  top: 0;
  left: 20px; /* Aligned with avatar center */
  height: 100%;
  width: 1px; /* Thinner line like YouTube */
}

/* Comment and reply items positioning */
.comment-item, .reply-item {
  position: relative;
  border-radius: 16px;
}

/* Nested replies - consistent styling for all levels */
.reply-item .comment-replies::before {
  left: 15px;
}

.reply-item {
  display: flex;
  gap: 16px;
  padding: 2px 0;
  background-color: transparent;
  margin-bottom: 0;
  transition: background-color 0.2s ease;
  position: relative;
}

/* No horizontal connection line in YouTube style */

.reply-item:hover {
  background-color: var(--hover-bg, rgba(245, 247, 251, 0.2));
}

.reply-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: rgba(63, 66, 84, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #3f4254;
  font-size: 16px;
  flex-shrink: 0;
  overflow: hidden;
}

.reply-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.reply-content {
  flex: 1;
}

.reply-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.reply-author {
  font-weight: 600;
  font-size: 13px;
  color: var(--text-color, #333);
}

.reply-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.reply-time {
  font-size: 11px;
  color: var(--text-secondary, #666);
}

.reply-edited {
  font-style: italic;
  color: var(--text-muted, #999);
  font-size: 10px;
}

.reply-actions {
  display: flex;
  gap: 6px;
}

.people-item {
  padding: 15px;
  background-color: var(--content-bg, #f5f7fb);
  border-radius: 12px;
  flex: 1;
  min-width: 150px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  gap: 16px;
}

.people-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
}

.people-item:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, transparent 0%, transparent 95%, var(--primary-blue, #4c6ef5) 95%);
  opacity: 0.1;
  z-index: 0;
}

.people-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: rgba(76, 110, 245, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-blue, #4c6ef5);
  font-size: 20px;
  flex-shrink: 0;
  position: relative;
  z-index: 1;
  transition: transform 0.2s ease;
}

.people-item:hover .people-avatar {
  transform: scale(1.1);
}

.people-info {
  flex: 1;
  position: relative;
  z-index: 1;
}

.people-role {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color, #333);
  margin-bottom: 8px;
  position: relative;
  z-index: 1;
}

.people-id {
  font-size: 13px;
  color: var(--text-secondary, #666);
  position: relative;
  z-index: 1;
}

/* Executors Container Styles */
.executors-container {
  display: flex;
  flex-direction: column;
  background-color: var(--content-bg, #f5f7fb);
  border-radius: 12px;
  padding: 16px;
  margin-top: 8px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.executors-container:hover {
  transform: translateY(-4px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
}

.executors-container:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, transparent 0%, transparent 95%, var(--primary-blue, #4c6ef5) 95%);
  opacity: 0.1;
  z-index: 0;
}

.executors-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 12px;
  position: relative;
  z-index: 1;
}

.executors-icon {
  color: var(--primary-blue, #4c6ef5);
  font-size: 20px;
}

.executors-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color, #333);
}

.executors-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  position: relative;
  z-index: 1;
}

.executor-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  background-color: rgba(255, 255, 255, 0.6);
  border-radius: 8px;
  transition: transform 0.2s ease, background-color 0.2s ease;
}

.executor-item:hover {
  transform: translateX(4px);
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.executor-item:hover .executor-avatar {
  transform: scale(1.1);
}

.executor-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: rgba(76, 110, 245, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-blue, #4c6ef5);
  font-size: 16px;
  flex-shrink: 0;
  transition: transform 0.2s ease;
}

.executor-info {
  flex: 1;
}

.executor-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-secondary, #666);
  display: block;
  margin-bottom: 4px;
}

.executor-badge {
  display: inline-block;
  font-size: 10px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 4px;
  background-color: rgba(76, 110, 245, 0.1);
  color: var(--primary-blue, #4c6ef5);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Grid layout for when there are many executors */
@media (min-width: 768px) {
  .executors-list.many-executors {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }
}

/* Responsive Adjustments */
@media (max-width: 1200px) {
  .request-detail-body {
    grid-template-columns: 3fr 2fr;
  }
}

@media (max-width: 992px) {
  .request-detail-body {
    grid-template-columns: 1fr;
  }

  .request-title-section {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .request-title-right {
    justify-content: flex-start;
  }

  .request-actions {
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .request-info-section,
  .people-section,
  .comment-section,
  .timeline-section {
    grid-column: 1;
  }

  .request-info-section {
    grid-row: 1;
  }

  .people-section {
    grid-row: 2;
  }

  .comment-section {
    grid-row: 3;
    display: flex;
    flex-direction: column;
    flex: 1;
  }

  .timeline-section {
    grid-row: 4;
  }
}

@media (max-width: 768px) {
  .request-detail-container {
    padding: 16px;
    border-radius: 0;
    box-shadow: none;
  }

  .request-detail-status-flow {
    overflow-x: auto;
    padding-bottom: 15px;
    margin: 16px 0;
    justify-content: flex-start;
  }

  .status-step {
    min-width: 100px;
  }

  .request-detail-grid {
    grid-template-columns: 1fr;
  }

  .detail-item {
    padding-bottom: 10px;
  }

  .request-detail-body {
    gap: 16px;
  }
}

/* Modern Status Badge Enhancements */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 30px;
  font-size: 13px;
  font-weight: 600;
  text-transform: capitalize;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.status-badge svg {
  font-size: 16px;
}

.status-badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.status-badge.pending {
  background-color: #fff8e1;
  color: #f57c00;
  /* border-left: 3px solid #f57c00; */
}

.status-badge.reviewed {
  background-color: #e0f7fa;
  color: #0097a7;
  /* border-left: 3px solid #0097a7; */
}

.status-badge.approved {
  background-color: #e8f5e9;
  color: #2e7d32;
  /* border-left: 3px solid #2e7d32; */
}

.status-badge.in_progress {
  background-color: #e3f2fd;
  color: #1565c0;
  /* border-left: 3px solid #1565c0; */
}

.status-badge.done {
  background-color: #e8f5e9;
  color: #2e7d32;
  /* border-left: 3px solid #2e7d32; */
}

.status-badge.rejected {
  background-color: #ffebee;
  color: #c62828;
  /* border-left: 3px solid #c62828; */
}

/* Dark Mode Specific Overrides */
[data-theme="dark"] .request-detail-container {
  background-color: var(--content-bg);
  box-shadow: var(--card-shadow);
}

/* Dark mode for action buttons */
[data-theme="dark"] .btn-reject {
  background-color: rgba(198, 40, 40, 0.2);
  color: #e57373;
}

[data-theme="dark"] .btn-reject:hover {
  background-color: rgba(198, 40, 40, 0.3);
}

[data-theme="dark"] .btn-approve {
  background-color: rgba(46, 125, 50, 0.2);
  color: #81c784;
}

[data-theme="dark"] .btn-approve:hover {
  background-color: rgba(46, 125, 50, 0.3);
}

[data-theme="dark"] .btn-review {
  background-color: rgba(0, 151, 167, 0.2);
  color: #4dd0e1;
}

[data-theme="dark"] .btn-review:hover {
  background-color: rgba(0, 151, 167, 0.3);
}

[data-theme="dark"] .btn-process {
  background-color: rgba(21, 101, 192, 0.2);
  color: #64b5f6;
}

[data-theme="dark"] .btn-process:hover {
  background-color: rgba(21, 101, 192, 0.3);
}

[data-theme="dark"] .btn-complete {
  background-color: rgba(46, 125, 50, 0.2);
  color: #81c784;
}

[data-theme="dark"] .btn-complete:hover {
  background-color: rgba(46, 125, 50, 0.3);
}

[data-theme="dark"] .status-step.active .step-icon {
  background-color: rgba(76, 175, 80, 0.2);
}

[data-theme="dark"] .status-step.current .step-icon {
  box-shadow: 0 2px 10px rgba(76, 175, 80, 0.4);
  background-color: #457e46;
}

[data-theme="dark"] .status-step.active:last-child .step-connector.active {
  background-color: #c62828;
}

[data-theme="dark"] .status-step.active[data-step-key="rejected"] .step-icon {
  background-color: rgba(198, 40, 40, 0.2);
}

[data-theme="dark"] .status-step.current[data-step-key="rejected"] .step-icon {
  background-color: #c65c5c;
  box-shadow: 0 2px 10px rgba(198, 40, 40, 0.4);
}

/* Status badges in dark mode */
[data-theme="dark"] .status-badge.pending {
  background-color: rgba(245, 124, 0, 0.2);
  color: #ffb74d;
}

[data-theme="dark"] .status-badge.reviewed {
  background-color: rgba(0, 151, 167, 0.2);
  color: #4dd0e1;
}

[data-theme="dark"] .status-badge.approved {
  background-color: rgba(46, 125, 50, 0.2);
  color: #81c784;
}

[data-theme="dark"] .status-badge.in_progress {
  background-color: rgba(21, 101, 192, 0.2);
  color: #64b5f6;
}

[data-theme="dark"] .status-badge.done {
  background-color: rgba(46, 125, 50, 0.2);
  color: #81c784;
}

[data-theme="dark"] .status-badge.rejected {
  background-color: rgba(198, 40, 40, 0.2);
  color: #e57373;
}

/* Dark mode for timeline status */
[data-theme="dark"] .timeline-status.pending {
  background-color: rgba(245, 124, 0, 0.2);
  color: #ffb74d;
}

[data-theme="dark"] .timeline-status.reviewed {
  background-color: rgba(0, 151, 167, 0.2);
  color: #4dd0e1;
}

[data-theme="dark"] .timeline-status.approved {
  background-color: rgba(46, 125, 50, 0.2);
  color: #81c784;
}

[data-theme="dark"] .timeline-status.in_progress {
  background-color: rgba(21, 101, 192, 0.2);
  color: #64b5f6;
}

[data-theme="dark"] .timeline-status.done {
  background-color: rgba(46, 125, 50, 0.2);
  color: #81c784;
}

[data-theme="dark"] .timeline-status.rejected {
  background-color: rgba(198, 40, 40, 0.2);
  color: #e57373;
}

/* Dark mode for comment section */
[data-theme="dark"] .comment-item {
  background-color: transparent;
}

[data-theme="dark"] .comment-input {
  background-color: var(--content-bg);
  border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .comment-input::placeholder {
  color: rgba(255, 255, 255, 0.4);
}

[data-theme="dark"] .comment-submit::after {
  color: rgba(255, 255, 255, 0.4);
}

/* Dark mode for code blocks in comments */
[data-theme="dark"] .comment-text code,
[data-theme="dark"] .comment-text pre,
[data-theme="dark"] .reply-text code,
[data-theme="dark"] .reply-text pre {
  background-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
}

[data-theme="dark"] .comment-input:focus,
[data-theme="dark"] .comment-edit-input:focus,
[data-theme="dark"] .comment-reply-input:focus {
  border-color: var(--primary-blue, #4c6ef5);
  box-shadow: 0 0 0 2px rgba(76, 110, 245, 0.2);
}

[data-theme="dark"] .comment-submit:disabled {
  background-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.3);
}

[data-theme="dark"] .comment-edit-input,
[data-theme="dark"] .comment-reply-input {
  background-color: var(--content-bg);
  border-color: rgba(255, 255, 255, 0.1);
  color: var(--text-color);
}

[data-theme="dark"] .btn-cancel {
  background-color: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.7);
}

[data-theme="dark"] .btn-cancel:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Process Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-container {
  background-color: var(--content-bg, white);
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  animation: modalFadeIn 0.3s ease;
}

.process-modal {
  max-width: 550px;
}

.process-modal-atlas-report {
  max-width: 750px;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid var(--border-color, #eee);
}

.modal-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--text-color, #333);
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  color: var(--text-secondary, #666);
  cursor: pointer;
  padding: 0;
  line-height: 1;
  transition: color 0.2s ease;
}

.modal-close:hover {
  color: var(--text-color, #333);
}

.modal-body {
  padding: 24px;
}

.process-confirmation p {
  margin-bottom: 16px;
  color: var(--text-color, #333);
}

.process-steps-preview {
  text-align: left;
  margin: 20px auto;
  max-width: 500px;
  padding-left: 30px;
}

.process-steps-preview li {
  margin-bottom: 10px;
  color: var(--text-secondary, #666);
}

/* Executor Tasks List in Process Modal */
.executor-tasks-list {
  margin-top: 8px;
  margin-left: 16px;
  padding-left: 8px;
  list-style-type: none;
}

.executor-task-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
  padding: 6px 10px;
  background-color: rgba(255, 255, 255, 0.6);
  border-radius: 6px;
  transition: transform 0.2s ease, background-color 0.2s ease;
}

.executor-task-item:hover {
  background-color: rgba(255, 255, 255, 0.8);
  transform: translateX(4px);
}

.executor-task-name {
  font-weight: 500;
  font-size: 13px;
}

.executor-task-type {
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid var(--border-color, #eee);
  background-color: white;
  font-size: 12px;
  cursor: pointer;
}

/* Executor Progress in Process Steps */
.executor-progress-list {
  margin-top: 12px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.executor-progress-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.executor-progress-name {
  font-size: 12px;
  min-width: 80px;
  font-weight: 500;
}

.executor-progress-bar {
  flex: 1;
  height: 6px;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
  overflow: hidden;
}

.executor-progress-fill {
  height: 100%;
  background-color: var(--primary-blue, #4c6ef5);
  border-radius: 3px;
  transition: width 0.5s ease;
}



.modal-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 24px;
}

.btn-secondary {
  background-color: var(--hover-bg, #f5f7fb);
  color: var(--text-secondary, #666);
  border: 1px solid var(--border-color, #eee);
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background-color: var(--hover-bg-darker, #e9ecf3);
  transform: translateY(-2px);
}

.process-steps {
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin-bottom: 30px;
}

.process-step {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px;
  border-radius: 12px;
  background-color: var(--hover-bg, #f5f7fb);
  transition: all 0.3s ease;
  opacity: 0.6;
}

.process-step.active {
  opacity: 1;
  background-color: var(--hover-bg, #f5f7fb);
}

.process-step.current {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transform: translateX(8px);
}

.process-step .step-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: var(--content-bg, white);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-muted, #999);
  font-size: 16px;
  flex-shrink: 0;
  transition: all 0.3s ease;
  border: 1px solid var(--border-color, #eee);
}

.process-step.active .step-icon {
  color: #4caf50;
  background-color: rgba(76, 175, 80, 0.1);
  border-color: #4caf50;
}

.process-step.current .step-icon {
  color: white;
  background-color: #4caf50;
  border-color: #4caf50;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.step-content {
  flex: 1;
}

.step-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color, #333);
  margin-bottom: 4px;
}

.step-description {
  font-size: 14px;
  color: var(--text-secondary, #666);
}

.process-complete {
  text-align: center;
  padding: 20px 0;
  animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.success-icon {
  font-size: 48px;
  color: #4caf50;
  margin-bottom: 16px;
}

.success-message {
  font-size: 18px;
  font-weight: 500;
  color: var(--text-color, #333);
  margin-bottom: 24px;
}

/* Executor Result List */
.executor-result-list {
  width: 100%;
  margin: 20px 0;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.executor-result-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.executor-result-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.executor-result-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: rgba(76, 110, 245, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-blue, #4c6ef5);
  font-size: 16px;
}

.executor-result-info {
  flex: 1;
}

.executor-result-name {
  font-weight: 500;
  margin-bottom: 4px;
  font-size: 14px;
}

.executor-result-details {
  display: flex;
  gap: 8px;
}

.executor-result-badge {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
  background-color: rgba(76, 110, 245, 0.1);
  color: var(--primary-blue, #4c6ef5);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.executor-result-duration {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--text-secondary, #666);
}

.executor-result-status {
  color: #4caf50;
  font-size: 18px;
}

.process-error {
  text-align: center;
  padding: 20px 0;
}

.error-icon {
  font-size: 48px;
  color: #f44336;
  margin-bottom: 16px;
}

.error-message {
  font-size: 16px;
  color: var(--text-secondary, #666);
  margin-bottom: 24px;
}

.spin {
  animation: spin 1.5s linear infinite;
}

/* Dark mode for process modal */
[data-theme="dark"] .modal-container {
  background-color: var(--content-bg);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .modal-header {
  border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .process-step {
  background-color: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .process-step .step-icon {
  background-color: var(--content-bg);
  border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .process-step.active .step-icon {
  background-color: rgba(76, 175, 80, 0.2);
  border-color: #81c784;
  color: #81c784;
}

[data-theme="dark"] .process-step.current .step-icon {
  background-color: #2e7d32;
  border-color: #2e7d32;
  color: white;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.2);
}

[data-theme="dark"] .success-icon {
  color: #81c784;
}

[data-theme="dark"] .success-message {
  color: var(--text-color);
}

/* [data-theme="dark"] .comment-reply-form {
  border-left-color: rgba(255, 255, 255, 0.1);
} */

/* Dark mode for executors */
/* [data-theme="dark"] .executors-container {
  background-color: rgba(255, 255, 255, 0.05);
} */

[data-theme="dark"] .executor-item {
  background-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .executor-item:hover {
  background-color: rgba(255, 255, 255, 0.15);
}

[data-theme="dark"] .executor-badge {
  background-color: rgba(76, 110, 245, 0.2);
  color: #64b5f6;
}

/* Dark mode for process request with executors */
[data-theme="dark"] .executor-task-item {
  background-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .executor-task-item:hover {
  background-color: rgba(255, 255, 255, 0.15);
}

[data-theme="dark"] .executor-task-type {
  background-color: rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
}

[data-theme="dark"] .executor-progress-bar {
  background-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .executor-progress-fill {
  background-color: #64b5f6;
}



[data-theme="dark"] .executor-result-item {
  background-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .executor-result-badge {
  background-color: rgba(76, 110, 245, 0.2);
  color: #64b5f6;
}

[data-theme="dark"] .executor-result-duration {
  background-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.7);
}

/* Report Needed Table Styles */
.report-needed-table {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-top: 8px;
}

/* Summary Stats */
.report-summary {
  background-color: var(--hover-bg, #f8f9fa);
  border-radius: 12px;
  padding: 12px;
  border: 1px solid var(--border-color, #e9ecef);
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
}

.summary-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 12px;
  background-color: var(--section-bg, white);
  border-radius: 8px;
  border: 1px solid var(--border-color, #e9ecef);
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: var(--primary-blue, #4c6ef5);
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-secondary, #666);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Items by Owner */
.items-by-owner {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.owner-section {
  border: 1px solid var(--border-color, #e9ecef);
  border-radius: 12px;
  overflow: hidden;
  background-color: var(--section-bg, white);
}

.owner-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background-color: var(--hover-bg, #f8f9fa);
  border-bottom: 1px solid var(--border-color, #e9ecef);
}

.owner-icon {
  color: var(--primary-blue, #4c6ef5);
  font-size: 18px;
}

.owner-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color, #333);
  margin: 0;
  flex: 1;
}

.owner-items-count {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-secondary, #666);
  background-color: var(--section-bg, white);
  padding: 4px 8px;
  border-radius: 12px;
  border: 1px solid var(--border-color, #e9ecef);
}

/* Table Sections */
.items-table-section {
  padding: 0;
}

.table-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  padding: 8px 12px;
  background-color: var(--hover-bg, #f8f9fa);
  border-bottom: 1px solid var(--border-color, #e9ecef);
}

.table-header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.table-header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.table-icon {
  color: var(--primary-blue, #4c6ef5);
  font-size: 16px;
}

.table-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-color, #333);
}

/* Table Styles */
.items-table {
  overflow-x: auto;
}

.items-table table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.items-table th {
  background-color: var(--hover-bg, #f8f9fa);
  color: var(--text-secondary, #666);
  font-weight: 600;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding: 8px 12px;
  text-align: left;
  border-bottom: 1px solid var(--border-color, #e9ecef);
}

.items-table td {
  padding: 8px 12px;
  border-bottom: 1px solid var(--border-color, #e9ecef);
  vertical-align: middle;
}

.items-table tbody tr:hover {
  background-color: var(--hover-bg, #f8f9fa);
}

.item-name {
  font-weight: 500;
  color: var(--text-color, #333);
  max-width: 300px;
  word-break: break-word;
}

.item-updated {
  color: var(--text-secondary, #666);
  font-size: 13px;
  white-space: nowrap;
}

.item-completed {
  text-align: center;
}

.atlas-link {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  color: var(--primary-blue, #4c6ef5);
  text-decoration: none;
  font-size: 13px;
  font-weight: 500;
  padding: 6px 12px;
  border-radius: 6px;
  border: 1px solid var(--primary-blue, #4c6ef5);
  transition: all 0.2s ease;
}

.atlas-link:hover {
  background-color: var(--primary-blue, #4c6ef5);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(76, 110, 245, 0.3);
}

/* Request Timestamp */
.request-timestamp {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 12px 16px;
  background-color: var(--hover-bg, #f8f9fa);
  border-radius: 8px;
  border: 1px solid var(--border-color, #e9ecef);
}

.timestamp-icon {
  color: var(--text-secondary, #666);
  font-size: 14px;
}

.request-timestamp small {
  color: var(--text-secondary, #666);
  font-size: 12px;
}

/* Dark Theme Support */
[data-theme="dark"] .report-summary {
  background-color: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

/* [data-theme="dark"] .summary-stat {
  background-color: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
} */

[data-theme="dark"] .owner-section {
  background-color: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .owner-header,
[data-theme="dark"] .table-header {
  background-color: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .items-table th {
  background-color: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .items-table td {
  border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .items-table tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .atlas-link {
  border-color: #64b5f6;
  color: #64b5f6;
}

[data-theme="dark"] .atlas-link:hover {
  background-color: #64b5f6;
  color: #1a1a1a;
}

[data-theme="dark"] .request-timestamp {
  background-color: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .summary-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .items-table {
    font-size: 12px;
  }

  .items-table th,
  .items-table td {
    padding: 8px 12px;
  }

  .atlas-link {
    font-size: 12px;
    padding: 4px 8px;
  }
}

/* Header Checkbox for Check All */
.table-header-checkbox {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 600;
  color: var(--text-secondary, #666);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  cursor: pointer;
  user-select: none;
  transition: color 0.2s ease;
}

.table-header-checkbox:hover {
  color: var(--text-color, #333);
}

.table-header-checkbox input[type="checkbox"] {
  width: 16px;
  height: 16px;
  cursor: pointer;
  accent-color: var(--primary-blue, #4c6ef5);
}

/* Process Modal specific styles */
.process-modal-report-table .table-header-checkbox {
  font-size: 11px;
}

.process-modal-report-table .table-header-checkbox input[type="checkbox"] {
  width: 14px;
  height: 14px;
}

/* No pending items message */
.no-pending-items {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  background-color: var(--hover-bg, #f8f9fa);
  border-radius: 12px;
  border: 1px solid var(--border-color, #e9ecef);
}

.completion-icon {
  font-size: 48px;
  color: #4caf50;
  margin-bottom: 16px;
}

.no-pending-items h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color, #333);
  margin: 0 0 8px 0;
}

.no-pending-items p {
  font-size: 14px;
  color: var(--text-secondary, #666);
  margin: 0;
  line-height: 1.5;
}

/* Dark Theme Support for header checkbox */
[data-theme="dark"] .table-header-checkbox {
  color: #bdbdbd;
}

[data-theme="dark"] .table-header-checkbox:hover {
  color: #e0e0e0;
}

[data-theme="dark"] .no-pending-items {
  background-color: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

/* Dark theme for disabled primary button */
[data-theme="dark"] .btn-primary:disabled {
  background-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.4);
}
